<?php
// <PERSON><PERSON>m tra users trong database
require_once 'app/config/database.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    
    echo "=== CHECKING USERS IN DATABASE ===\n\n";
    
    $query = "SELECT id, username, fullname, role FROM account";
    $stmt = $db->prepare($query);
    $stmt->execute();
    $users = $stmt->fetchAll(PDO::FETCH_OBJ);
    
    echo "Found " . count($users) . " users:\n";
    echo "ID | Username | Fullname | Role\n";
    echo "---|----------|----------|-----\n";
    
    foreach ($users as $user) {
        echo sprintf("%2d | %-8s | %-15s | %s\n", 
            $user->id, 
            $user->username, 
            $user->fullname, 
            $user->role
        );
    }
    
    echo "\n=== TESTING PASSWORDS ===\n\n";
    
    // Test các password có thể có cho admin
    $adminPasswords = ['admin', 'password', '123456', 'Admin'];
    
    foreach ($users as $user) {
        if ($user->role === 'admin') {
            echo "Testing passwords for admin user '{$user->username}':\n";
            
            // Lấy password hash từ database
            $query = "SELECT password FROM account WHERE username = :username";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':username', $user->username);
            $stmt->execute();
            $result = $stmt->fetch(PDO::FETCH_OBJ);
            
            if ($result) {
                echo "Password hash: " . substr($result->password, 0, 50) . "...\n";
                
                foreach ($adminPasswords as $testPassword) {
                    if (password_verify($testPassword, $result->password)) {
                        echo "✅ Password '{$testPassword}' works!\n";
                    } else {
                        echo "❌ Password '{$testPassword}' failed\n";
                    }
                }
            }
            echo "\n";
        }
    }
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>
