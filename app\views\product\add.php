
<?php include 'app/views/shares/header.php'; ?> 

<h1>Th<PERSON><PERSON> sản phẩm mới</h1>

<div class="alert alert-danger d-none" id="error-message"></div>
<div class="alert alert-success d-none" id="success-message"></div>

<form id="add-product-form">
    <div class="form-group mb-3">
        <label for="name">Tên sản phẩm:</label>
        <input type="text" id="name" name="name" class="form-control" required minlength="3">
        <div class="invalid-feedback">Vui lòng nhập tên sản phẩm (ít nhất 3 ký tự)</div>
    </div>

    <div class="form-group mb-3">
        <label for="description">Mô tả:</label>
        <textarea id="description" name="description" class="form-control" required minlength="10"></textarea>
        <div class="invalid-feedback">Vui lòng nhập mô tả sản phẩm (ít nhất 10 ký tự)</div>
    </div>

    <div class="form-group mb-3">
        <label for="price">Giá:</label>
        <input type="number" id="price" name="price" class="form-control" min="0" step="1000" required>
        <div class="invalid-feedback">Vui lòng nhập giá hợp lệ</div>
    </div>

    <div class="form-group mb-3">
        <label for="category_id">Danh mục:</label>
        <select id="category_id" name="category_id" class="form-control" required>
            <option value="">-- Chọn danh mục --</option>
        </select>
        <div class="invalid-feedback">Vui lòng chọn danh mục</div>
    </div>

    <button type="submit" class="btn btn-primary" id="submit-btn">
        <span class="spinner-border spinner-border-sm d-none" id="loading-spinner"></span>
        Thêm sản phẩm
    </button>
</form>

<a href="/webbanhang/Product/list" class="btn btn-secondary mt-2">Quay lại danh sách sản phẩm</a>

<?php include 'app/views/shares/footer.php'; ?>

<script>
document.addEventListener("DOMContentLoaded", function() {
    const form = document.getElementById('add-product-form');
    const submitBtn = document.getElementById('submit-btn');
    const loadingSpinner = document.getElementById('loading-spinner');
    const errorMessage = document.getElementById('error-message');
    const successMessage = document.getElementById('success-message');
    const categorySelect = document.getElementById('category_id');

    function showLoading() {
        submitBtn.disabled = true;
        loadingSpinner.classList.remove('d-none');
    }

    function hideLoading() {
        submitBtn.disabled = false;
        loadingSpinner.classList.add('d-none');
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
        successMessage.classList.add('d-none');
    }

    function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.classList.remove('d-none');
        errorMessage.classList.add('d-none');
    }

    // Load categories
    fetch('/webbanhang/api/category')
        .then(response => {
            if (!response.ok) throw new Error('Không thể tải danh mục');
            return response.json();
        })
        .then(categories => {
            categories.forEach(category => {
                const option = document.createElement('option');
                option.value = category.id;
                option.textContent = category.name;
                categorySelect.appendChild(option);
            });
        })
        .catch(error => showError('Lỗi tải danh mục: ' + error.message));

    form.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Reset messages
        errorMessage.classList.add('d-none');
        successMessage.classList.add('d-none');

        // Form validation
        if (!form.checkValidity()) {
            form.classList.add('was-validated');
            return;
        }

        const formData = new FormData(this);
        const jsonData = Object.fromEntries(formData.entries());
        
        // Convert price to number
        jsonData.price = Number(jsonData.price);

        showLoading();

        fetch('/webbanhang/api/product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(jsonData)
        })
        .then(response => {
            if (!response.ok) throw new Error('Lỗi khi thêm sản phẩm');
            return response.json();
        })
        .then(data => {
            if (data.message === 'Product created successfully') {
                showSuccess('Thêm sản phẩm thành công!');
                setTimeout(() => {
                    location.href = '/webbanhang/Product';
                }, 1000);
            } else {
                throw new Error(data.error || 'Thêm sản phẩm thất bại');
            }
        })
        .catch(error => {
            showError(error.message);
        })
        .finally(() => {
            hideLoading();
        });
    });
});
</script>