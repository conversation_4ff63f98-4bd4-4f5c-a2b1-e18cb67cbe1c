<?php
// Test login endpoint only
header('Content-Type: text/plain; charset=utf-8');

function testLogin($username, $password) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/WEBBANHANG/account/checkLogin');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'username' => $username,
        'password' => $password
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'headers' => $header,
        'body' => $body
    ];
}

echo "=== TEST LOGIN ENDPOINT ===\n\n";

// Test với admin
echo "1. Testing Admin login:\n";
$result = testLogin('Admin', 'admin');
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . $result['body'] . "\n\n";

echo "1b. Testing admin (lowercase) login:\n";
$result = testLogin('admin', 'admin');
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . $result['body'] . "\n\n";

// Test với user
echo "2. Testing user login:\n";
$result = testLogin('user', 'user');
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . $result['body'] . "\n\n";

// Test với invalid credentials
echo "3. Testing invalid login:\n";
$result = testLogin('invalid', 'invalid');
echo "HTTP Code: " . $result['http_code'] . "\n";
echo "Response: " . $result['body'] . "\n\n";
?>
