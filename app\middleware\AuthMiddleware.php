<?php

require_once 'app/utils/JWTHandler.php';

class AuthMiddleware
{
    private $jwtHandler;
    
    public function __construct()
    {
        $this->jwtHandler = new JWTHandler();
    }
    
    /**
     * Kiểm tra JWT token từ header Authorization
     */
    public function authenticate()
    {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
        
        if (!$authHeader) {
            $this->sendUnauthorizedResponse('Missing Authorization header');
            return false;
        }
        
        // Kiểm tra format: Bearer <token>
        if (!preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            $this->sendUnauthorizedResponse('Invalid Authorization header format');
            return false;
        }
        
        $token = $matches[1];
        $userData = $this->jwtHandler->decode($token);
        
        if (!$userData) {
            $this->sendUnauthorizedResponse('Invalid or expired token');
            return false;
        }
        
        // Lưu thông tin user vào global để sử dụng trong controller
        $GLOBALS['current_user'] = $userData;
        return true;
    }
    
    /**
     * Kiểm tra quyền admin
     */
    public function requireAdmin()
    {
        if (!$this->authenticate()) {
            return false;
        }
        
        $user = $GLOBALS['current_user'];
        if (!isset($user['role']) || $user['role'] !== 'admin') {
            $this->sendForbiddenResponse('Admin access required');
            return false;
        }
        
        return true;
    }
    
    /**
     * Kiểm tra user đã đăng nhập (bất kỳ role nào)
     */
    public function requireAuth()
    {
        return $this->authenticate();
    }
    
    /**
     * Trả về response 401 Unauthorized
     */
    private function sendUnauthorizedResponse($message = 'Unauthorized')
    {
        http_response_code(401);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'error' => 'Unauthorized',
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Trả về response 403 Forbidden
     */
    private function sendForbiddenResponse($message = 'Forbidden')
    {
        http_response_code(403);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'error' => 'Forbidden',
            'message' => $message
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Lấy thông tin user hiện tại
     */
    public static function getCurrentUser()
    {
        return $GLOBALS['current_user'] ?? null;
    }
    
    /**
     * Kiểm tra token có hợp lệ không (không exit)
     */
    public function validateToken()
    {
        $headers = getallheaders();
        $authHeader = $headers['Authorization'] ?? $headers['authorization'] ?? null;
        
        if (!$authHeader || !preg_match('/Bearer\s+(.*)$/i', $authHeader, $matches)) {
            return false;
        }
        
        $token = $matches[1];
        $userData = $this->jwtHandler->decode($token);
        
        if ($userData) {
            $GLOBALS['current_user'] = $userData;
            return true;
        }
        
        return false;
    }
}
?>
