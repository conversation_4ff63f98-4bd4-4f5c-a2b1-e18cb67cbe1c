</div>
    </main>

    <!-- Toast Container -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

    <!-- Footer -->
    <footer class="footer mt-auto">
        <div class="container">
            <div class="row g-4">
                <div class="col-lg-4">
                    <h5 class="mb-3">
                        <i class="bi bi-shop"></i> TechStore
                    </h5>
                    <p class="text-white-50">
                        Cửa hàng công nghệ uy tín, chất lượng hàng đầu Việt Nam
                    </p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white-50"><i class="bi bi-facebook fs-5"></i></a>
                        <a href="#" class="text-white-50"><i class="bi bi-twitter fs-5"></i></a>
                        <a href="#" class="text-white-50"><i class="bi bi-instagram fs-5"></i></a>
                    </div>
                </div>
                <div class="col-lg-2">
                    <h6 class="text-uppercase mb-3">Sản phẩm</h6>
                    <ul class="list-unstyled">
                        <li><a href="/webbanhang/Product" class="text-white-50 text-decoration-none">Tất cả sản phẩm</a></li>
                        <li><a href="/webbanhang/Product/cart" class="text-white-50 text-decoration-none">Giỏ hàng</a></li>
                    </ul>
                </div>
                <div class="col-lg-3">
                    <h6 class="text-uppercase mb-3">Liên hệ</h6>
                    <div class="text-white-50">
                        <p><i class="bi bi-geo-alt"></i> 123 Đường ABC, Quận XYZ, TP.HCM</p>
                        <p><i class="bi bi-telephone"></i> 0123 456 789</p>
                        <p><i class="bi bi-envelope"></i> <EMAIL></p>
                    </div>
                </div>
            </div>
            <hr class="my-4 bg-secondary">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0 text-white-50">&copy; 2024 TechStore. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toast function
        function showToast(message, type = 'success') {
            const toastContainer = document.querySelector('.toast-container');
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            toast.addEventListener('hidden.bs.toast', () => toast.remove());
        }
    </script>
</body>
</html>