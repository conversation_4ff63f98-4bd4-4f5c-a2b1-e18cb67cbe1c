<?php
// Test API bị chặn khi không có token
header('Content-Type: text/plain; charset=utf-8');

$baseUrl = 'http://localhost/WEBBANHANG/api';

function testAPIWithoutToken($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'headers' => $header,
        'body' => $body
    ];
}

function testAPIWithToken($url, $token, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    
    $headers = ['Authorization: Bearer ' . $token];
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        $headers[] = 'Content-Type: application/json';
    }
    
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    $header = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    curl_close($ch);
    
    return [
        'http_code' => $httpCode,
        'headers' => $header,
        'body' => $body
    ];
}

function getToken($username, $password) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, 'http://localhost/WEBBANHANG/account/checkLogin');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'username' => $username,
        'password' => $password
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    curl_close($ch);
    
    $data = json_decode($response, true);
    return $data['token'] ?? null;
}

echo "=== TEST API AUTHENTICATION ===\n\n";

// Test 1: API bị chặn khi không có token
echo "1. TEST API WITHOUT TOKEN (Should be BLOCKED)\n";
echo "================================================\n";

$endpoints = [
    'GET /api/product' => $baseUrl . '/product',
    'GET /api/product/1' => $baseUrl . '/product/1',
    'GET /api/category' => $baseUrl . '/category',
    'POST /api/product' => $baseUrl . '/product',
    'PUT /api/product/1' => $baseUrl . '/product/1',
    'DELETE /api/product/1' => $baseUrl . '/product/1'
];

foreach ($endpoints as $name => $url) {
    $method = explode(' ', $name)[0];
    $data = null;
    
    if ($method === 'POST' || $method === 'PUT') {
        $data = [
            'name' => 'Test Product',
            'description' => 'Test Description',
            'price' => 100000,
            'category_id' => 1
        ];
    }
    
    $result = testAPIWithoutToken($url, $method, $data);
    $status = $result['http_code'] === 401 ? '✅ BLOCKED' : '❌ NOT BLOCKED';
    
    echo sprintf("%-20s | HTTP %d | %s\n", $name, $result['http_code'], $status);
    
    if ($result['http_code'] !== 401) {
        echo "   Response: " . substr($result['body'], 0, 100) . "...\n";
    }
}

echo "\n";

// Test 2: API hoạt động khi có token hợp lệ
echo "2. TEST API WITH VALID TOKEN (Should WORK)\n";
echo "==========================================\n";

// Lấy token admin
$adminToken = getToken('testadmin', 'admin');
if ($adminToken) {
    echo "✅ Admin token obtained: " . substr($adminToken, 0, 30) . "...\n\n";
    
    foreach ($endpoints as $name => $url) {
        $method = explode(' ', $name)[0];
        $data = null;
        
        if ($method === 'POST' || $method === 'PUT') {
            $data = [
                'name' => 'Test Product with Token',
                'description' => 'Test Description with Token',
                'price' => 200000,
                'category_id' => 1
            ];
        }
        
        $result = testAPIWithToken($url, $adminToken, $method, $data);
        $status = $result['http_code'] < 400 ? '✅ SUCCESS' : '❌ FAILED';
        
        echo sprintf("%-20s | HTTP %d | %s\n", $name, $result['http_code'], $status);
        
        if ($result['http_code'] >= 400) {
            echo "   Error: " . substr($result['body'], 0, 100) . "...\n";
        }
    }
} else {
    echo "❌ Failed to get admin token\n";
}

echo "\n";

// Test 3: API với token user (chỉ GET được phép)
echo "3. TEST API WITH USER TOKEN (Only GET should work)\n";
echo "==================================================\n";

$userToken = getToken('user', 'user');
if ($userToken) {
    echo "✅ User token obtained: " . substr($userToken, 0, 30) . "...\n\n";
    
    foreach ($endpoints as $name => $url) {
        $method = explode(' ', $name)[0];
        $data = null;
        
        if ($method === 'POST' || $method === 'PUT') {
            $data = [
                'name' => 'Test Product User',
                'description' => 'Test Description User',
                'price' => 150000,
                'category_id' => 1
            ];
        }
        
        $result = testAPIWithToken($url, $userToken, $method, $data);
        
        if ($method === 'GET') {
            $status = $result['http_code'] < 400 ? '✅ SUCCESS (Expected)' : '❌ FAILED';
        } else {
            $status = $result['http_code'] === 403 ? '✅ FORBIDDEN (Expected)' : '❌ UNEXPECTED';
        }
        
        echo sprintf("%-20s | HTTP %d | %s\n", $name, $result['http_code'], $status);
    }
} else {
    echo "❌ Failed to get user token\n";
}

echo "\n=== TEST COMPLETED ===\n";
echo "\nSUMMARY:\n";
echo "- Without token: All APIs should return 401 Unauthorized\n";
echo "- With admin token: All APIs should work\n";
echo "- With user token: Only GET APIs should work, others return 403 Forbidden\n";
?>
