<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test JWT Authentication</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body { background: #f8f9fa; }
        .test-card { margin-bottom: 20px; }
        .test-result { font-family: monospace; background: #f8f9fa; padding: 10px; border-radius: 5px; margin-top: 10px; }
        .status-pass { color: #28a745; }
        .status-fail { color: #dc3545; }
        .status-pending { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1><i class="fas fa-shield-alt"></i> JWT Authentication Test</h1>
        <p class="text-muted">Kiểm tra hệ thống xác thực JWT</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-sign-in-alt"></i> Login Test</h5>
                    </div>
                    <div class="card-body">
                        <form id="loginForm">
                            <div class="mb-3">
                                <label class="form-label">Username</label>
                                <input type="text" class="form-control" id="username" value="admin">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Password</label>
                                <input type="password" class="form-control" id="password" value="admin">
                            </div>
                            <button type="submit" class="btn btn-primary">Login</button>
                        </form>
                        <div id="login-result" class="test-result" style="display:none;"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card test-card">
                    <div class="card-header">
                        <h5><i class="fas fa-key"></i> Token Info</h5>
                    </div>
                    <div class="card-body">
                        <div id="token-info">
                            <p class="text-muted">Chưa có token</p>
                        </div>
                        <button class="btn btn-secondary" onclick="clearToken()">Clear Token</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-vial"></i> API Tests</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <button class="btn btn-info w-100 mb-2" onclick="testGetProducts()">
                            GET Products
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-success w-100 mb-2" onclick="testCreateProduct()">
                            POST Product
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-warning w-100 mb-2" onclick="testUpdateProduct()">
                            PUT Product
                        </button>
                    </div>
                    <div class="col-md-3">
                        <button class="btn btn-danger w-100 mb-2" onclick="testDeleteProduct()">
                            DELETE Product
                        </button>
                    </div>
                </div>
                <div id="api-results" class="test-result" style="display:none;"></div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        let currentToken = null;
        let testProductId = null;
        
        $(document).ready(function() {
            updateTokenInfo();
            
            $('#loginForm').on('submit', function(e) {
                e.preventDefault();
                testLogin();
            });
        });
        
        function testLogin() {
            const username = $('#username').val();
            const password = $('#password').val();
            
            $.ajax({
                url: 'account/checkLogin',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({ username, password })
            })
            .done(function(response) {
                currentToken = response.token;
                localStorage.setItem('jwtToken', currentToken);
                
                $('#login-result').show().html(`
                    <div class="status-pass">✅ Login successful!</div>
                    <div><strong>Token:</strong> ${currentToken.substring(0, 50)}...</div>
                    <div><strong>User:</strong> ${JSON.stringify(response.user, null, 2)}</div>
                `);
                
                updateTokenInfo();
            })
            .fail(function(xhr) {
                $('#login-result').show().html(`
                    <div class="status-fail">❌ Login failed!</div>
                    <div><strong>Error:</strong> ${xhr.responseJSON?.message || xhr.statusText}</div>
                `);
            });
        }
        
        function updateTokenInfo() {
            const token = localStorage.getItem('jwtToken');
            
            if (token) {
                try {
                    // Decode JWT payload (simple base64 decode)
                    const payload = JSON.parse(atob(token.split('.')[1]));
                    const expiry = new Date(payload.exp * 1000);
                    const isExpired = Date.now() > payload.exp * 1000;
                    
                    $('#token-info').html(`
                        <div><strong>Status:</strong> <span class="${isExpired ? 'status-fail' : 'status-pass'}">${isExpired ? 'Expired' : 'Valid'}</span></div>
                        <div><strong>User:</strong> ${payload.data.username}</div>
                        <div><strong>Role:</strong> ${payload.data.role}</div>
                        <div><strong>Expires:</strong> ${expiry.toLocaleString()}</div>
                        <div><strong>Token:</strong> ${token.substring(0, 30)}...</div>
                    `);
                } catch (e) {
                    $('#token-info').html('<div class="status-fail">❌ Invalid token format</div>');
                }
            } else {
                $('#token-info').html('<p class="text-muted">Chưa có token</p>');
            }
        }
        
        function clearToken() {
            localStorage.removeItem('jwtToken');
            currentToken = null;
            updateTokenInfo();
            $('#login-result').hide();
            $('#api-results').hide();
        }
        
        function makeAuthenticatedRequest(url, method = 'GET', data = null) {
            const token = localStorage.getItem('jwtToken');
            const options = {
                url: url,
                method: method,
                headers: {}
            };
            
            if (token) {
                options.headers['Authorization'] = `Bearer ${token}`;
            }
            
            if (data) {
                options.contentType = 'application/json';
                options.data = JSON.stringify(data);
            }
            
            return $.ajax(options);
        }
        
        function testGetProducts() {
            makeAuthenticatedRequest('api/product')
                .done(function(response) {
                    showApiResult('GET Products', true, `Found ${response.length} products`);
                })
                .fail(function(xhr) {
                    showApiResult('GET Products', false, `${xhr.status}: ${xhr.responseJSON?.message || xhr.statusText}`);
                });
        }
        
        function testCreateProduct() {
            const productData = {
                name: 'Test Product JWT ' + Date.now(),
                description: 'Test product for JWT authentication',
                price: 99999,
                category_id: 1
            };
            
            makeAuthenticatedRequest('api/product', 'POST', productData)
                .done(function(response) {
                    showApiResult('POST Product', true, 'Product created successfully');
                    // Get the created product ID for update/delete tests
                    testGetProducts(); // Refresh to get latest product
                })
                .fail(function(xhr) {
                    showApiResult('POST Product', false, `${xhr.status}: ${xhr.responseJSON?.message || xhr.statusText}`);
                });
        }
        
        function testUpdateProduct() {
            if (!testProductId) {
                // Try to get a product ID first
                makeAuthenticatedRequest('api/product')
                    .done(function(response) {
                        if (response.length > 0) {
                            testProductId = response[response.length - 1].id;
                            performUpdateTest();
                        } else {
                            showApiResult('PUT Product', false, 'No products found to update');
                        }
                    });
            } else {
                performUpdateTest();
            }
        }
        
        function performUpdateTest() {
            const updateData = {
                name: 'Updated Test Product JWT',
                description: 'Updated description',
                price: 199999,
                category_id: 1
            };
            
            makeAuthenticatedRequest(`api/product/${testProductId}`, 'PUT', updateData)
                .done(function(response) {
                    showApiResult('PUT Product', true, 'Product updated successfully');
                })
                .fail(function(xhr) {
                    showApiResult('PUT Product', false, `${xhr.status}: ${xhr.responseJSON?.message || xhr.statusText}`);
                });
        }
        
        function testDeleteProduct() {
            if (!testProductId) {
                // Try to get a product ID first
                makeAuthenticatedRequest('api/product')
                    .done(function(response) {
                        if (response.length > 0) {
                            testProductId = response[response.length - 1].id;
                            performDeleteTest();
                        } else {
                            showApiResult('DELETE Product', false, 'No products found to delete');
                        }
                    });
            } else {
                performDeleteTest();
            }
        }
        
        function performDeleteTest() {
            makeAuthenticatedRequest(`api/product/${testProductId}`, 'DELETE')
                .done(function(response) {
                    showApiResult('DELETE Product', true, 'Product deleted successfully');
                    testProductId = null; // Reset for next test
                })
                .fail(function(xhr) {
                    showApiResult('DELETE Product', false, `${xhr.status}: ${xhr.responseJSON?.message || xhr.statusText}`);
                });
        }
        
        function showApiResult(operation, success, message) {
            const status = success ? 'status-pass' : 'status-fail';
            const icon = success ? '✅' : '❌';
            const timestamp = new Date().toLocaleTimeString();
            
            const resultHtml = `
                <div class="${status}">${icon} ${operation}: ${message}</div>
                <div class="text-muted">Time: ${timestamp}</div>
                <hr>
            `;
            
            $('#api-results').show().prepend(resultHtml);
        }
    </script>
</body>
</html>
