<?php include 'app/views/shares/header.php'; ?> 
<h1><PERSON><PERSON> s<PERSON><PERSON> sản phẩm</h1>
<div class="d-flex justify-content-between mb-3">
    <a href="/webbanhang/Product/add" class="btn btn-success">Thê<PERSON> sản phẩm mới</a>
    <div id="loading-indicator" class="d-none">
        <span class="spinner-border spinner-border-sm" role="status"></span>
        Đang tải...
    </div>
</div>

<div class="alert alert-danger d-none" id="error-message"></div>
<ul class="list-group" id="product-list"></ul>

<?php include 'app/views/shares/footer.php'; ?> 

<script> 
document.addEventListener("DOMContentLoaded", function() {
    const loadingIndicator = document.getElementById('loading-indicator');
    const errorMessage = document.getElementById('error-message');
    const productList = document.getElementById('product-list');

    function showLoading() {
        loadingIndicator.classList.remove('d-none');
    }

    function hideLoading() {
        loadingIndicator.classList.add('d-none');
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('d-none');
    }

    function loadProducts() {
        showLoading();
        errorMessage.classList.add('d-none');
        productList.innerHTML = '';

        fetch('/webbanhang/api/product')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (!data.length) {
                    productList.innerHTML = '<div class="alert alert-info">Không có sản phẩm nào.</div>';
                    return;
                }

                data.forEach(product => {
                    const productItem = document.createElement('li');
                    productItem.className = 'list-group-item';
                    productItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h2 class="h5 mb-2">
                                    <a href="/webbanhang/Product/show/${product.id}">${product.name}</a>
                                </h2>
                                <p class="mb-1">${product.description}</p>
                                <p class="mb-1"><strong>Giá:</strong> ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(product.price)}</p>
                                <p class="mb-1"><strong>Danh mục:</strong> ${product.category_name}</p>
                            </div>
                            <div class="btn-group">
                                <a href="/webbanhang/Product/edit/${product.id}" class="btn btn-warning btn-sm">
                                    <i class="fas fa-edit"></i> Sửa
                                </a>
                                <button class="btn btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                                    <i class="fas fa-trash"></i> Xóa
                                </button>
                            </div>
                        </div>
                    `;
                    productList.appendChild(productItem);
                });
            })
            .catch(error => {
                showError('Không thể tải danh sách sản phẩm: ' + error.message);
            })
            .finally(() => {
                hideLoading();
            });
    }

    loadProducts();

    window.deleteProduct = function(id) {
        if (!confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
            return;
        }

        showLoading();
        fetch(`/webbanhang/api/product/${id}`, {
            method: 'DELETE'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.message === 'Product deleted successfully') {
                loadProducts();
            } else {
                throw new Error(data.error || 'Xóa sản phẩm thất bại');
            }
        })
        .catch(error => {
            showError('Lỗi khi xóa sản phẩm: ' + error.message);
        })
        .finally(() => {
            hideLoading();
        });
    }
});
</script>