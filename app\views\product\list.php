
<?php include 'app/views/shares/header.php'; ?>

<div class="container">
    <h1 class="my-4"><PERSON><PERSON> s<PERSON>ch sản phẩm</h1>
    <a href="/webbanhang/Product/add" class="btn btn-success mb-3">Thê<PERSON> sản phẩm mới</a>
    <div id="loading" class="text-center d-none">
        <div class="spinner-border" role="status">
            <span class="sr-only">Loading...</span>
        </div>
    </div>
    <ul class="list-group" id="product-list"></ul>
</div>

<?php include 'app/views/shares/footer.php'; ?>

<script>
const showLoading = () => document.getElementById('loading').classList.remove('d-none');
const hideLoading = () => document.getElementById('loading').classList.add('d-none');

document.addEventListener("DOMContentLoaded", async function() {
    const token = localStorage.getItem('jwtToken');
    if (!token) {
        alert('Vui lòng đăng nhập');
        location.href = '/webbanhang/account/login';
        return;
    }

    try {
        showLoading();
        const response = await fetch('/webbanhang/api/product', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();
        const productList = document.getElementById('product-list');
        
        data.forEach(product => {
            const productItem = document.createElement('li');
            productItem.className = 'list-group-item';
            productItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">
                            <a href="/webbanhang/Product/show/${product.id}" class="text-decoration-none">
                                ${product.name}
                            </a>
                        </h5>
                        <p class="mb-1">${product.description}</p>
                        <p class="mb-1">Giá: ${new Intl.NumberFormat('vi-VN', { style: 'currency', currency: 'VND' }).format(product.price)}</p>
                        <small class="text-muted">Danh mục: ${product.category_name}</small>
                    </div>
                    <div class="btn-group" role="group">
                        <a href="/webbanhang/Product/edit/${product.id}" class="btn btn-warning btn-sm">Sửa</a>
                        <button class="btn btn-danger btn-sm ml-2" onclick="deleteProduct(${product.id})">Xóa</button>
                    </div>
                </div>
            `;
            productList.appendChild(productItem);
        });
    } catch (error) {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi tải danh sách sản phẩm');
    } finally {
        hideLoading();
    }
});

async function deleteProduct(id) {
    if (!confirm('Bạn có chắc chắn muốn xóa sản phẩm này?')) {
        return;
    }

    const token = localStorage.getItem('jwtToken');
    try {
        const response = await fetch(`/webbanhang/api/product/${id}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Network response was not ok');
        }

        const data = await response.json();
        if (data.message === 'Product deleted successfully') {
            location.reload();
        } else {
            throw new Error(data.message || 'Xóa sản phẩm thất bại');
        }
    } catch (error) {
        console.error('Error:', error);
        alert(error.message || 'Có lỗi xảy ra khi xóa sản phẩm');
    }
}
</script>