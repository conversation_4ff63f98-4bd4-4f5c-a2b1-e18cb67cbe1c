<?php

require_once('app/config/database.php');
require_once('app/models/AccountModel.php');

class AccountController {
    private $accountModel;
    private $db;

    public function __construct() {
        $this->db = (new Database())->getConnection();
        $this->accountModel = new AccountModel($this->db);
    }

    public function register() {
        include_once 'app/views/account/register.php';
    }

    public function login() {
        include_once 'app/views/account/login.php';
    }

    public function save() {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Lấy dữ liệu từ form đăng ký, sử dụng null coalescing operator (??) 
            // để gán giá trị mặc định nếu không tồn tại
            $username = $_POST['username'] ?? '';
            $fullName = $_POST['fullname'] ?? '';
            $password = $_POST['password'] ?? '';
            $confirmPassword = $_POST['confirmpassword'] ?? '';
            $role = $_POST['role'] ?? 'user';
            $errors = [];

            // Kiểm tra validation các trường dữ liệu
            if (empty($username)) {
                $errors['username'] = "Vui lòng nhập username!";
            }
            if (empty($fullName)) {
                $errors['fullname'] = "Vui lòng nhập fullname!";
            }
            if (empty($password)) {
                $errors['password'] = "Vui lòng nhập password!";
            }
            if ($password != $confirmPassword) {
                $errors['confirmPass'] = "Mật khẩu và xác nhận chưa khớp!";
            }
            if (!in_array($role, ['admin', 'user'])) {
                $role = 'user';
            }
            // Kiểm tra xem username đã tồn tại chưa
            if ($this->accountModel->getAccountByUsername($username)) {
                $errors['account'] = "Tài khoản này đã được đăng ký!";
            }

            if (count($errors) > 0) {
                // Nếu có lỗi, quay lại form đăng ký và hiển thị lỗi
                include_once 'app/views/account/register.php';
            } else {
                // Lưu tài khoản mới vào database
                $result = $this->accountModel->save($username, $fullName, $password, $role);
                if ($result) {
                    // Chuyển hướng đến trang đăng nhập sau khi đăng ký thành công
                    header('Location: /webbanhang/account/login');
                    exit;
                }
            }
        }
    }

    public function logout() {
        session_start();
        unset($_SESSION['username']);
        unset($_SESSION['role']);
        header('Location: /webbanhang/product');
        exit;
    }

    public function checkLogin() {
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Lấy thông tin đăng nhập từ form
            $username = $_POST['username'] ?? '';
            $password = $_POST['password'] ?? '';

            // Kiểm tra thông tin tài khoản trong database
            $account = $this->accountModel->getAccountByUsername($username);
            
            // Xác thực mật khẩu bằng password_verify để so sánh với mật khẩu đã hash
            if ($account && password_verify($password, $account->password)) {
                // Khởi tạo session và lưu thông tin người dùng
                session_start();
                if (!isset($_SESSION['username'])) {
                    $_SESSION['username'] = $account->username;
                    $_SESSION['role'] = $account->role;
                }
                // Chuyển hướng đến trang sản phẩm sau khi đăng nhập thành công
                header('Location: /webbanhang/product');
                exit;
            } else {
                // Hiển thị thông báo lỗi phù hợp nếu đăng nhập thất bại
                $error = $account ? "Mật khẩu không đúng!" : "Không tìm thấy tài khoản!";
                include_once 'app/views/account/login.php';
                exit;
            }
        }
    }
}
?>