# API Documentation - WEBBANHANG

## Base URL
```
http://localhost/WEBBANHANG/api
```

## Product API Endpoints

### 1. <PERSON><PERSON><PERSON> danh sách sản phẩm
**GET** `/api/product`

**Response:**
```json
[
    {
        "id": 1,
        "name": "Tên sản phẩm",
        "description": "<PERSON>ô tả sản phẩm",
        "price": "100000.00",
        "category_name": "Tên danh mục"
    }
]
```

### 2. <PERSON><PERSON><PERSON> sản phẩm theo ID
**GET** `/api/product/{id}`

**Response:**
```json
{
    "id": 1,
    "name": "Tên sản phẩm",
    "description": "<PERSON>ô tả sản phẩm",
    "price": "100000.00",
    "image": null,
    "category_id": 1
}
```

### 3. Thêm sản phẩm mới
**POST** `/api/product`

**Request Body:**
```json
{
    "name": "<PERSON>ên sản phẩm",
    "description": "<PERSON><PERSON> tả sản phẩm",
    "price": 100000,
    "category_id": 1
}
```

**Response Success (201):**
```json
{
    "message": "Product created successfully"
}
```

**Response Error (400):**
```json
{
    "errors": {
        "name": "Tên sản phẩm không được để trống",
        "price": "Giá sản phẩm không hợp lệ"
    }
}
```

### 4. Cập nhật sản phẩm
**PUT** `/api/product/{id}`

**Request Body:**
```json
{
    "name": "Tên sản phẩm mới",
    "description": "Mô tả mới",
    "price": 200000,
    "category_id": 1
}
```

**Response Success (200):**
```json
{
    "message": "Product updated successfully"
}
```

### 5. Xóa sản phẩm
**DELETE** `/api/product/{id}`

**Response Success (200):**
```json
{
    "message": "Product deleted successfully"
}
```

## Category API Endpoints

### 1. Lấy danh sách danh mục
**GET** `/api/category`

### 2. Lấy danh mục theo ID
**GET** `/api/category/{id}`

## Postman Testing

### Cấu hình Headers
```
Content-Type: application/json
Accept: application/json
```

### Test Cases

1. **GET Products:**
   - URL: `http://localhost/WEBBANHANG/api/product`
   - Method: GET

2. **POST Product:**
   - URL: `http://localhost/WEBBANHANG/api/product`
   - Method: POST
   - Body (raw JSON):
   ```json
   {
       "name": "Test Product",
       "description": "Test Description",
       "price": 50000,
       "category_id": 1
   }
   ```

3. **PUT Product:**
   - URL: `http://localhost/WEBBANHANG/api/product/1`
   - Method: PUT
   - Body (raw JSON):
   ```json
   {
       "name": "Updated Product",
       "description": "Updated Description",
       "price": 75000,
       "category_id": 1
   }
   ```

4. **DELETE Product:**
   - URL: `http://localhost/WEBBANHANG/api/product/1`
   - Method: DELETE

## Error Handling

Tất cả API endpoints đều trả về JSON response với proper HTTP status codes:

- **200**: Success
- **201**: Created
- **400**: Bad Request (validation errors)
- **404**: Not Found
- **500**: Internal Server Error

## Validation Rules

### Product:
- `name`: Required, không được để trống
- `description`: Required, không được để trống  
- `price`: Required, phải là số và >= 0
- `category_id`: Optional

## Notes

- Tất cả response đều sử dụng UTF-8 encoding
- JSON response được format với `JSON_UNESCAPED_UNICODE` để hiển thị tiếng Việt đúng
- API sử dụng output buffering để tránh lỗi JSON malformed
