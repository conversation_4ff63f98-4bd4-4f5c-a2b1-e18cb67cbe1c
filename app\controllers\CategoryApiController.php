<?php

require_once('app/config/database.php');
require_once('app/models/CategoryModel.php');
require_once('app/middleware/AuthMiddleware.php');

class CategoryApiController
{
    private $categoryModel;
    private $db;
    private $authMiddleware;

    public function __construct()
    {
        $this->db = (new Database())->getConnection();
        $this->categoryModel = new CategoryModel($this->db);
        $this->authMiddleware = new AuthMiddleware();
    }

    // Lấy danh sách danh mục
    public function index()
    {
        // Yêu cầu authentication cho tất cả users
        if (!$this->authMiddleware->requireAuth()) {
            return;
        }

        header('Content-Type: application/json; charset=utf-8');
        $categories = $this->categoryModel->getCategories();
        echo json_encode($categories, JSON_UNESCAPED_UNICODE);
    }

    // L<PERSON>y danh mục theo ID
    public function show($id)
    {
        // Yêu cầu authentication cho tất cả users
        if (!$this->authMiddleware->requireAuth()) {
            return;
        }

        header('Content-Type: application/json; charset=utf-8');
        $category = $this->categoryModel->getCategoryById($id);

        if ($category) {
            echo json_encode($category, JSON_UNESCAPED_UNICODE);
        } else {
            http_response_code(404);
            echo json_encode(['message' => 'Category not found'], JSON_UNESCAPED_UNICODE);
        }
    }
}