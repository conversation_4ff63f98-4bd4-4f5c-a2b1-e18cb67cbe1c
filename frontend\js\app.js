// WEBBANHANG Frontend Application
$(document).ready(function() {
    // Configuration
    const API_BASE_URL = '../api';
    let currentProducts = [];
    let currentCategories = [];
    let currentPage = 1;
    const itemsPerPage = 10;
    let deleteProductId = null;

    // Initialize application
    init();

    function init() {
        loadCategories();
        loadProducts();
        setupEventListeners();
    }

    // Event Listeners
    function setupEventListeners() {
        // Search functionality
        $('#searchInput').on('input', debounce(filterProducts, 300));
        
        // Category filter
        $('#categoryFilter').on('change', filterProducts);
        
        // Sort functionality
        $('#sortBy').on('change', sortProducts);
        
        // Form validation
        $('#productForm input, #productForm textarea, #productForm select').on('input change', clearValidationErrors);
    }

    // API Functions
    function makeApiCall(endpoint, method = 'GET', data = null) {
        const token = localStorage.getItem('jwtToken');

        const options = {
            url: `${API_BASE_URL}${endpoint}`,
            method: method,
            dataType: 'json',
            headers: {},
            beforeSend: function() {
                if (method !== 'GET') {
                    showLoading();
                }
            },
            complete: function() {
                hideLoading();
            }
        };

        // Thêm JWT token vào header nếu có
        if (token) {
            options.headers['Authorization'] = `Bearer ${token}`;
        }

        if (data) {
            options.contentType = 'application/json';
            options.data = JSON.stringify(data);
        }

        return $.ajax(options)
            .fail(function(xhr) {
                // Xử lý lỗi authentication
                if (xhr.status === 401) {
                    showAlert('Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.', 'warning');
                    localStorage.removeItem('jwtToken');
                    setTimeout(() => {
                        window.location.href = '../account/login';
                    }, 2000);
                } else if (xhr.status === 403) {
                    showAlert('Bạn không có quyền thực hiện thao tác này.', 'danger');
                }
            });
    }

    // Load Products
    function loadProducts() {
        showLoading();
        
        makeApiCall('/product')
            .done(function(response) {
                currentProducts = response;
                displayProducts(currentProducts);
                updatePagination();
                showAlert('Tải danh sách sản phẩm thành công!', 'success', 2000);
            })
            .fail(function(xhr) {
                console.error('Error loading products:', xhr);
                showAlert('Lỗi khi tải danh sách sản phẩm: ' + getErrorMessage(xhr), 'danger');
                $('#productsTableBody').html('<tr><td colspan="6" class="text-center text-muted">Không thể tải dữ liệu</td></tr>');
            });
    }

    // Load Categories
    function loadCategories() {
        makeApiCall('/category')
            .done(function(response) {
                currentCategories = response;
                populateCategorySelects();
            })
            .fail(function(xhr) {
                console.error('Error loading categories:', xhr);
                showAlert('Lỗi khi tải danh mục: ' + getErrorMessage(xhr), 'warning');
            });
    }

    // Display Products
    function displayProducts(products) {
        const tbody = $('#productsTableBody');
        tbody.empty();

        if (!products || products.length === 0) {
            tbody.html('<tr><td colspan="6" class="text-center text-muted">Không có sản phẩm nào</td></tr>');
            return;
        }

        // Pagination
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedProducts = products.slice(startIndex, endIndex);

        paginatedProducts.forEach(function(product) {
            const row = createProductRow(product);
            tbody.append(row);
        });

        // Add fade-in animation
        tbody.find('tr').addClass('fade-in');
    }

    // Create Product Row
    function createProductRow(product) {
        const price = formatCurrency(product.price);
        const description = truncateText(product.description, 50);
        const categoryName = product.category_name || 'Chưa phân loại';

        return `
            <tr data-product-id="${product.id}">
                <td><span class="badge bg-secondary">${product.id}</span></td>
                <td>
                    <strong>${escapeHtml(product.name)}</strong>
                </td>
                <td>
                    <span class="text-truncate-2" title="${escapeHtml(product.description)}">
                        ${escapeHtml(description)}
                    </span>
                </td>
                <td><span class="price">${price}</span></td>
                <td><span class="badge bg-info">${escapeHtml(categoryName)}</span></td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewProduct(${product.id})" title="Xem chi tiết">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editProduct(${product.id})" title="Chỉnh sửa">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteProduct(${product.id}, '${escapeHtml(product.name)}')" title="Xóa">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }

    // Populate Category Selects
    function populateCategorySelects() {
        const selects = ['#categoryFilter', '#productCategory'];
        
        selects.forEach(function(selector) {
            const $select = $(selector);
            const currentValue = $select.val();
            
            // Keep first option for filter, clear for product form
            if (selector === '#categoryFilter') {
                $select.find('option:not(:first)').remove();
            } else {
                $select.find('option:not(:first)').remove();
            }
            
            currentCategories.forEach(function(category) {
                $select.append(`<option value="${category.id}">${escapeHtml(category.name)}</option>`);
            });
            
            // Restore previous value
            if (currentValue) {
                $select.val(currentValue);
            }
        });
    }

    // Filter Products
    function filterProducts() {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const categoryId = $('#categoryFilter').val();
        
        let filteredProducts = currentProducts.filter(function(product) {
            const matchesSearch = !searchTerm || 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm);
            
            const matchesCategory = !categoryId || 
                product.category_id == categoryId;
            
            return matchesSearch && matchesCategory;
        });
        
        currentPage = 1; // Reset to first page
        displayProducts(filteredProducts);
        updatePagination(filteredProducts.length);
    }

    // Sort Products
    function sortProducts() {
        const sortBy = $('#sortBy').val();
        const searchTerm = $('#searchInput').val().toLowerCase();
        const categoryId = $('#categoryFilter').val();
        
        // Get filtered products first
        let products = currentProducts.filter(function(product) {
            const matchesSearch = !searchTerm || 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm);
            
            const matchesCategory = !categoryId || 
                product.category_id == categoryId;
            
            return matchesSearch && matchesCategory;
        });
        
        // Sort products
        products.sort(function(a, b) {
            switch(sortBy) {
                case 'name':
                    return a.name.localeCompare(b.name);
                case 'price':
                    return parseFloat(a.price) - parseFloat(b.price);
                case 'id':
                    return a.id - b.id;
                default:
                    return 0;
            }
        });
        
        displayProducts(products);
        updatePagination(products.length);
    }

    // Pagination
    function updatePagination(totalItems = null) {
        const total = totalItems !== null ? totalItems : currentProducts.length;
        const totalPages = Math.ceil(total / itemsPerPage);
        const pagination = $('#pagination');
        
        pagination.empty();
        
        if (totalPages <= 1) return;
        
        // Previous button
        const prevDisabled = currentPage === 1 ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${prevDisabled}">
                <a class="page-link" href="#" onclick="changePage(${currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `);
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            const active = i === currentPage ? 'active' : '';
            pagination.append(`
                <li class="page-item ${active}">
                    <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
                </li>
            `);
        }
        
        // Next button
        const nextDisabled = currentPage === totalPages ? 'disabled' : '';
        pagination.append(`
            <li class="page-item ${nextDisabled}">
                <a class="page-link" href="#" onclick="changePage(${currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `);
    }

    // Change Page
    window.changePage = function(page) {
        const searchTerm = $('#searchInput').val().toLowerCase();
        const categoryId = $('#categoryFilter').val();
        
        let filteredProducts = currentProducts.filter(function(product) {
            const matchesSearch = !searchTerm || 
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm);
            
            const matchesCategory = !categoryId || 
                product.category_id == categoryId;
            
            return matchesSearch && matchesCategory;
        });
        
        const totalPages = Math.ceil(filteredProducts.length / itemsPerPage);
        
        if (page >= 1 && page <= totalPages) {
            currentPage = page;
            displayProducts(filteredProducts);
            updatePagination(filteredProducts.length);
        }
    };

    // Modal Functions
    window.openAddModal = function() {
        $('#modalTitle').text('Thêm sản phẩm mới');
        $('#productForm')[0].reset();
        $('#productId').val('');
        clearValidationErrors();
    };

    window.viewProduct = function(id) {
        makeApiCall(`/product/${id}`)
            .done(function(product) {
                showProductDetails(product);
            })
            .fail(function(xhr) {
                showAlert('Lỗi khi tải thông tin sản phẩm: ' + getErrorMessage(xhr), 'danger');
            });
    };

    window.editProduct = function(id) {
        makeApiCall(`/product/${id}`)
            .done(function(product) {
                $('#modalTitle').text('Chỉnh sửa sản phẩm');
                $('#productId').val(product.id);
                $('#productName').val(product.name);
                $('#productDescription').val(product.description);
                $('#productPrice').val(product.price);
                $('#productCategory').val(product.category_id || '');
                
                clearValidationErrors();
                $('#productModal').modal('show');
            })
            .fail(function(xhr) {
                showAlert('Lỗi khi tải thông tin sản phẩm: ' + getErrorMessage(xhr), 'danger');
            });
    };

    window.deleteProduct = function(id, name) {
        deleteProductId = id;
        $('#deleteProductName').text(name);
        $('#deleteModal').modal('show');
    };

    window.confirmDelete = function() {
        if (!deleteProductId) return;

        makeApiCall(`/product/${deleteProductId}`, 'DELETE')
            .done(function() {
                $('#deleteModal').modal('hide');
                showAlert('Xóa sản phẩm thành công!', 'success');
                loadProducts();
                deleteProductId = null;
            })
            .fail(function(xhr) {
                showAlert('Lỗi khi xóa sản phẩm: ' + getErrorMessage(xhr), 'danger');
            });
    };

    window.saveProduct = function() {
        if (!validateForm()) return;

        const productData = {
            name: $('#productName').val().trim(),
            description: $('#productDescription').val().trim(),
            price: parseFloat($('#productPrice').val()),
            category_id: $('#productCategory').val() || null
        };

        const productId = $('#productId').val();
        const isEdit = productId !== '';

        const endpoint = isEdit ? `/product/${productId}` : '/product';
        const method = isEdit ? 'PUT' : 'POST';

        makeApiCall(endpoint, method, productData)
            .done(function() {
                $('#productModal').modal('hide');
                const message = isEdit ? 'Cập nhật sản phẩm thành công!' : 'Thêm sản phẩm thành công!';
                showAlert(message, 'success');
                loadProducts();
            })
            .fail(function(xhr) {
                handleFormErrors(xhr);
            });
    };

    // Validation Functions
    function validateForm() {
        clearValidationErrors();
        let isValid = true;

        const name = $('#productName').val().trim();
        const description = $('#productDescription').val().trim();
        const price = $('#productPrice').val();

        if (!name) {
            showFieldError('productName', 'nameError', 'Tên sản phẩm không được để trống');
            isValid = false;
        }

        if (!description) {
            showFieldError('productDescription', 'descriptionError', 'Mô tả không được để trống');
            isValid = false;
        }

        if (!price || parseFloat(price) < 0) {
            showFieldError('productPrice', 'priceError', 'Giá sản phẩm không hợp lệ');
            isValid = false;
        }

        return isValid;
    }

    function clearValidationErrors() {
        $('.form-control, .form-select').removeClass('is-invalid');
        $('.invalid-feedback').text('');
    }

    function showFieldError(fieldId, errorId, message) {
        $(`#${fieldId}`).addClass('is-invalid');
        $(`#${errorId}`).text(message);
    }

    function handleFormErrors(xhr) {
        if (xhr.status === 400 && xhr.responseJSON && xhr.responseJSON.errors) {
            const errors = xhr.responseJSON.errors;

            if (errors.name) showFieldError('productName', 'nameError', errors.name);
            if (errors.description) showFieldError('productDescription', 'descriptionError', errors.description);
            if (errors.price) showFieldError('productPrice', 'priceError', errors.price);
        } else {
            showAlert('Lỗi khi lưu sản phẩm: ' + getErrorMessage(xhr), 'danger');
        }
    }

    // Product Details Modal
    function showProductDetails(product) {
        const categoryName = currentCategories.find(c => c.id == product.category_id)?.name || 'Chưa phân loại';

        const detailsHtml = `
            <div class="modal fade" id="productDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Chi tiết sản phẩm</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6><i class="fas fa-tag"></i> Thông tin cơ bản</h6>
                                    <table class="table table-borderless">
                                        <tr><td><strong>ID:</strong></td><td>${product.id}</td></tr>
                                        <tr><td><strong>Tên:</strong></td><td>${escapeHtml(product.name)}</td></tr>
                                        <tr><td><strong>Giá:</strong></td><td class="price">${formatCurrency(product.price)}</td></tr>
                                        <tr><td><strong>Danh mục:</strong></td><td><span class="badge bg-info">${escapeHtml(categoryName)}</span></td></tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h6><i class="fas fa-info-circle"></i> Mô tả</h6>
                                    <p class="text-muted">${escapeHtml(product.description)}</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                            <button type="button" class="btn btn-warning" onclick="editProduct(${product.id}); $('#productDetailsModal').modal('hide');">
                                <i class="fas fa-edit"></i> Chỉnh sửa
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        $('#productDetailsModal').remove();

        // Add new modal to body
        $('body').append(detailsHtml);

        // Show modal
        $('#productDetailsModal').modal('show');

        // Remove modal from DOM when hidden
        $('#productDetailsModal').on('hidden.bs.modal', function() {
            $(this).remove();
        });
    }

    // Utility Functions
    function showAlert(message, type = 'info', duration = 5000) {
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${getAlertIcon(type)}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        $('#alert-container').append(alertHtml);

        // Auto dismiss
        if (duration > 0) {
            setTimeout(function() {
                $(`#${alertId}`).alert('close');
            }, duration);
        }
    }

    function getAlertIcon(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    function showLoading() {
        $('#loading').show();
    }

    function hideLoading() {
        $('#loading').hide();
    }

    function formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND'
        }).format(amount);
    }

    function truncateText(text, maxLength) {
        if (!text) return '';
        return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
    }

    function escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function getErrorMessage(xhr) {
        if (xhr.responseJSON && xhr.responseJSON.error) {
            return xhr.responseJSON.error;
        }
        if (xhr.responseJSON && xhr.responseJSON.message) {
            return xhr.responseJSON.message;
        }
        return `HTTP ${xhr.status}: ${xhr.statusText}`;
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Global functions for window scope
    window.loadProducts = loadProducts;
});
