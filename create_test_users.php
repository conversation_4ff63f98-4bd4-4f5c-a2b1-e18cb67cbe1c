<?php
// Tạo user test cho authentication
require_once 'app/config/database.php';
require_once 'app/models/AccountModel.php';

try {
    $database = new Database();
    $db = $database->getConnection();
    $accountModel = new AccountModel($db);
    
    echo "=== CREATING TEST USERS ===\n\n";
    
    // Tạo admin user
    echo "1. Creating admin user...\n";
    $adminResult = $accountModel->save('testadmin', 'Test Administrator', 'admin', 'admin');
    if ($adminResult) {
        echo "✅ Test admin user created successfully\n";
    } else {
        echo "❌ Test admin user creation failed (may already exist)\n";
    }
    
    // Tạo regular user
    echo "2. Creating regular user...\n";
    $userResult = $accountModel->save('user', 'Regular User', 'user', 'user');
    if ($userResult) {
        echo "✅ Regular user created successfully\n";
    } else {
        echo "❌ Regular user creation failed (may already exist)\n";
    }
    
    // Kiểm tra users đã tạo
    echo "\n3. Checking created users:\n";
    $admin = $accountModel->getAccountByUsername('testadmin');
    if ($admin) {
        echo "✅ Test admin found: {$admin->username} (role: {$admin->role})\n";
    } else {
        echo "❌ Test admin not found\n";
    }
    
    $user = $accountModel->getAccountByUsername('user');
    if ($user) {
        echo "✅ User found: {$user->username} (role: {$user->role})\n";
    } else {
        echo "❌ User not found\n";
    }
    
    echo "\n=== COMPLETED ===\n";
    echo "You can now login with:\n";
    echo "- Admin: username=testadmin, password=admin\n";
    echo "- User: username=user, password=user\n";
    
} catch (Exception $e) {
    echo "ERROR: " . $e->getMessage() . "\n";
}
?>
