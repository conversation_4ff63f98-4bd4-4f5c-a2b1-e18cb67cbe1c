<?php

require_once('app/config/database.php');
require_once('app/models/ProductModel.php');
require_once('app/models/CategoryModel.php');
require_once('app/middleware/AuthMiddleware.php');

class ProductApiController
{
    private $productModel;
    private $db;
    private $authMiddleware;

    public function __construct()
    {
        // Bắt đầu output buffering để tránh output không mong muốn
        if (!ob_get_level()) {
            ob_start();
        }

        $this->db = (new Database())->getConnection();
        $this->productModel = new ProductModel($this->db);
        $this->authMiddleware = new AuthMiddleware();
    }

    private function cleanOutput()
    {
        // Xóa bất kỳ output buffer nào trước khi trả về JSON
        while (ob_get_level()) {
            ob_end_clean();
        }
    }

    // <PERSON><PERSON><PERSON><PERSON> s<PERSON> sản phẩm
    public function index()
    {
        // <PERSON>êu cầu authentication cho tất cả users
        if (!$this->authMiddleware->requireAuth()) {
            return;
        }

        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $products = $this->productModel->getProducts();

            // Đảm bảo encoding UTF-8
            $json = json_encode($products, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

            if ($json === false) {
                throw new Exception('JSON encoding failed: ' . json_last_error_msg());
            }

            echo $json;
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Lấy thông tin sản phẩm theo ID
    public function show($id)
    {
        // Yêu cầu authentication cho tất cả users
        if (!$this->authMiddleware->requireAuth()) {
            return;
        }

        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $product = $this->productModel->getProductById($id);

            if ($product) {
                echo json_encode($product, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
            } else {
                http_response_code(404);
                echo json_encode(['message' => 'Product not found'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Thêm sản phẩm mới
    public function store()
    {
        // Kiểm tra authentication - chỉ admin mới được thêm sản phẩm
        if (!$this->authMiddleware->requireAdmin()) {
            return;
        }

        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON: ' . json_last_error_msg());
            }

            $name = $data['name'] ?? '';
            $description = $data['description'] ?? '';
            $price = $data['price'] ?? '';
            $category_id = $data['category_id'] ?? null;

            $result = $this->productModel->addProduct(
                $name,
                $description,
                $price,
                $category_id
            );

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode(['errors' => $result], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(201);
                echo json_encode(['message' => 'Product created successfully'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Cập nhật sản phẩm theo ID
    public function update($id)
    {
        // Kiểm tra authentication - chỉ admin mới được cập nhật sản phẩm
        if (!$this->authMiddleware->requireAdmin()) {
            return;
        }

        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $data = json_decode(file_get_contents("php://input"), true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON: ' . json_last_error_msg());
            }

            $name = $data['name'] ?? '';
            $description = $data['description'] ?? '';
            $price = $data['price'] ?? '';
            $category_id = $data['category_id'] ?? null;

            $result = $this->productModel->updateProduct(
                $id,
                $name,
                $description,
                $price,
                $category_id
            );

            if (is_array($result)) {
                http_response_code(400);
                echo json_encode(['errors' => $result], JSON_UNESCAPED_UNICODE);
            } elseif ($result) {
                echo json_encode(['message' => 'Product updated successfully'], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['message' => 'Product update failed'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }

    // Xóa sản phẩm theo ID
    public function destroy($id)
    {
        // Kiểm tra authentication - chỉ admin mới được xóa sản phẩm
        if (!$this->authMiddleware->requireAdmin()) {
            return;
        }

        $this->cleanOutput();
        header('Content-Type: application/json; charset=utf-8');

        try {
            $result = $this->productModel->deleteProduct($id);

            if ($result) {
                echo json_encode(['message' => 'Product deleted successfully'], JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['message' => 'Product deletion failed'], JSON_UNESCAPED_UNICODE);
            }
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
        }
    }
}