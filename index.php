<?php

session_start();
require_once 'app/models/ProductModel.php';
require_once 'app/helpers/SessionHelper.php';
require_once 'app/controllers/ProductApiController.php';
require_once 'app/controllers/CategoryApiController.php';

// Xử lý URL
$url = $_GET['url'] ?? '';
$url = rtrim($url, '/');
$url = filter_var($url, FILTER_SANITIZE_URL);
$url = explode('/', $url);

// Thiết lập controller và action mặc định
$controllerName = isset($url[0]) && $url[0] != '' ? ucfirst($url[0]) . 'Controller' : 'ProductController';
$action = isset($url[1]) && $url[1] != '' ? $url[1] : 'index';

// Xử lý các yêu cầu API
if ($controllerName === 'ApiController' && isset($url[1])) {
    try {
        $apiControllerName = ucfirst($url[1]) . 'ApiController';
        $controllerPath = 'app/controllers/' . $apiControllerName . '.php';
        
        if (!file_exists($controllerPath)) {
            throw new Exception('Controller not found', 404);
        }

        require_once $controllerPath;
        $controller = new $apiControllerName();
        
        $method = $_SERVER['REQUEST_METHOD'];
        $id = $url[2] ?? null;

        // Xác định action dựa trên HTTP method
        switch ($method) {
            case 'GET':
                $action = $id ? 'show' : 'index';
                break;
            case 'POST':
                $action = 'store';
                break;
            case 'PUT':
                if (!$id) throw new Exception('ID is required for update', 400);
                $action = 'update';
                break;
            case 'DELETE':
                if (!$id) throw new Exception('ID is required for delete', 400);
                $action = 'destroy';
                break;
            default:
                throw new Exception('Method Not Allowed', 405);
        }

        if (!method_exists($controller, $action)) {
            throw new Exception('Action not found', 404);
        }

        // Thực thi action
        $result = call_user_func_array([$controller, $action], $id ? [$id] : []);
        
        // Trả về kết quả dạng JSON
        header('Content-Type: application/json');
        echo json_encode($result);
        
    } catch (Exception $e) {
        http_response_code($e->getCode() ?: 500);
        echo json_encode(['error' => $e->getMessage()]);
    }
    exit;
}

// Xử lý các yêu cầu không phải API
try {
    $controllerPath = 'app/controllers/' . $controllerName . '.php';
    
    if (!file_exists($controllerPath)) {
        throw new Exception("Controller '$controllerName' not found");
    }

    require_once $controllerPath;
    
    if (!class_exists($controllerName)) {
        throw new Exception("Class '$controllerName' not found");
    }

    $controller = new $controllerName();
    
    if (!method_exists($controller, $action)) {
        throw new Exception("Action '$action' not found in $controllerName");
    }

    // Thực thi action với các tham số
    call_user_func_array([$controller, $action], array_slice($url, 2));
    
} catch (Exception $e) {
    // Xử lý lỗi
    header('Content-Type: text/html; charset=utf-8');
    die($e->getMessage());
}